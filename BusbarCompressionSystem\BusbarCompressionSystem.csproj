﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\PropertyChanged.Fody.4.1.0\build\PropertyChanged.Fody.props" Condition="Exists('..\packages\PropertyChanged.Fody.4.1.0\build\PropertyChanged.Fody.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{FC482D2F-A3D1-41AC-993F-83B5ED95551F}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>BusbarCompressionSystem</RootNamespace>
    <AssemblyName>BusbarCompressionSystem</AssemblyName>
    <TargetFrameworkVersion>v4.7</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <ProjectTypeGuids>{60dc8134-eba5-43b8-bcc9-bb4bc16c2548};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <WarningLevel>4</WarningLevel>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>x64</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Castle.Core">
      <HintPath>..\oracletest20250506\Castle.Core.dll</HintPath>
    </Reference>
    <Reference Include="CommonServiceLocator, Version=2.0.2.0, Culture=neutral, PublicKeyToken=489b6accfaf20ef0, processorArchitecture=MSIL">
      <HintPath>..\packages\CommonServiceLocator.2.0.2\lib\net47\CommonServiceLocator.dll</HintPath>
    </Reference>
    <Reference Include="GalaSoft.MvvmLight, Version=5.4.1.0, Culture=neutral, PublicKeyToken=e7570ab207bcb616, processorArchitecture=MSIL">
      <HintPath>..\packages\MvvmLightLibs.5.4.1.1\lib\net45\GalaSoft.MvvmLight.dll</HintPath>
    </Reference>
    <Reference Include="GalaSoft.MvvmLight.Extras, Version=5.4.1.0, Culture=neutral, PublicKeyToken=669f0b5e8f868abf, processorArchitecture=MSIL">
      <HintPath>..\packages\MvvmLightLibs.5.4.1.1\lib\net45\GalaSoft.MvvmLight.Extras.dll</HintPath>
    </Reference>
    <Reference Include="GalaSoft.MvvmLight.Platform, Version=5.4.1.0, Culture=neutral, PublicKeyToken=5f873c45e98af8a1, processorArchitecture=MSIL">
      <HintPath>..\packages\MvvmLightLibs.5.4.1.1\lib\net45\GalaSoft.MvvmLight.Platform.dll</HintPath>
    </Reference>
    <Reference Include="GetSNByMP">
      <HintPath>..\oracletest20250506\GetSNByMP.dll</HintPath>
    </Reference>
    <Reference Include="halcondotnet">
      <HintPath>dll\halcondotnet.dll</HintPath>
    </Reference>
    <Reference Include="HslCommunication">
      <HintPath>dll\HslCommunication.dll</HintPath>
    </Reference>
    <Reference Include="Kcommon">
      <HintPath>dll\Kcommon.dll</HintPath>
    </Reference>
    <Reference Include="MESAutoLineClient">
      <HintPath>..\oracletest20250506\MESAutoLineClient.dll</HintPath>
    </Reference>
    <Reference Include="MvCameraControl.Net">
      <HintPath>dll\MvCameraControl.Net.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\oracletest20250506\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="OnlyOnce">
      <HintPath>dll\OnlyOnce.dll</HintPath>
    </Reference>
    <Reference Include="Oracle.ManagedDataAccess">
      <HintPath>..\oracletest20250506\Oracle.ManagedDataAccess.dll</HintPath>
    </Reference>
    <Reference Include="Panuon.WPF, Version=1.0.3.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Panuon.WPF.1.0.3\lib\net462\Panuon.WPF.dll</HintPath>
    </Reference>
    <Reference Include="Panuon.WPF.UI, Version=1.1.17.3, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Panuon.WPF.UI.1.1.17.3\lib\net462\Panuon.WPF.UI.dll</HintPath>
    </Reference>
    <Reference Include="PropertyChanged, Version=4.1.0.0, Culture=neutral, PublicKeyToken=ee3ee20bcf148ddd, processorArchitecture=MSIL">
      <HintPath>..\packages\PropertyChanged.Fody.4.1.0\lib\net40\PropertyChanged.dll</HintPath>
    </Reference>
    <Reference Include="Scanner">
      <HintPath>dll\Scanner.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.IO" />
    <Reference Include="System.Runtime" />
    <Reference Include="System.Threading.Tasks" />
    <Reference Include="System.Windows.Interactivity, Version=4.5.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\MvvmLightLibs.5.4.1.1\lib\net45\System.Windows.Interactivity.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xaml">
      <RequiredTargetFramework>4.0</RequiredTargetFramework>
    </Reference>
    <Reference Include="WindowsBase" />
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
  </ItemGroup>
  <ItemGroup>
    <ApplicationDefinition Include="App.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </ApplicationDefinition>
    <Compile Include="Model\DataModel.cs" />
    <Compile Include="Model\FaraVision\Control\Toolcontrol.xaml.cs">
      <DependentUpon>Toolcontrol.xaml</DependentUpon>
    </Compile>
    <Compile Include="Model\FaraVision\FaraVisionDataModel.cs" />
    <Compile Include="Model\FaraVision\Convertor.cs" />
    <Compile Include="Model\FaraVision\Hobject2Bitmap.cs" />
    <Compile Include="Model\FaraVision\Inputbox.xaml.cs">
      <DependentUpon>Inputbox.xaml</DependentUpon>
    </Compile>
    <Compile Include="Model\FaraVision\InputPassword.xaml.cs">
      <DependentUpon>InputPassword.xaml</DependentUpon>
    </Compile>
    <Compile Include="Model\FaraVision\ModelWindow.xaml.cs">
      <DependentUpon>ModelWindow.xaml</DependentUpon>
    </Compile>
    <Compile Include="Model\FaraVision\SettingForm.xaml.cs">
      <DependentUpon>SettingForm.xaml</DependentUpon>
    </Compile>
    <Compile Include="Model\FaraVision\Tool\QRcode.cs" />
    <Compile Include="Model\FaraVision\Tool\ToolModel.cs" />
    <Compile Include="Model\FaraVision\newPrj.xaml.cs">
      <DependentUpon>newPrj.xaml</DependentUpon>
    </Compile>
    <Compile Include="Model\Processmodel.cs" />
    <Compile Include="Model\Record1\Productinfo.cs" />
    <Compile Include="Model\Record1\ProductInfoRecord.cs" />
    <Compile Include="Model\Record1\Record.cs" />
    <Compile Include="Model\Record1\TestModel.cs" />
    <Compile Include="Model\RecordModel.cs" />
    <Compile Include="Model\Setting1\ImageSaveSetting.cs" />
    <Compile Include="Model\Setting1\SETTING_DATA.cs" />
    <Compile Include="Model\Setting1\TcpSetting.cs" />
    <Compile Include="Model\SettingModel.cs" />
    <Compile Include="ViewModel\MainViewModel.cs" />
    <Compile Include="ViewModel\MainViewModel_FaraVision.cs" />
    <Compile Include="ViewModel\ViewModelLocator.cs" />
    <Compile Include="View\TVTestControl.xaml.cs">
      <DependentUpon>TVTestControl.xaml</DependentUpon>
    </Compile>
    <Compile Include="WOCODEINPUTFORM.xaml.cs">
      <DependentUpon>WOCODEINPUTFORM.xaml</DependentUpon>
    </Compile>
    <Page Include="MainWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Compile Include="App.xaml.cs">
      <DependentUpon>App.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="MainWindow.xaml.cs">
      <DependentUpon>MainWindow.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Page Include="Model\FaraVision\Control\Toolcontrol.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Model\FaraVision\Inputbox.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Model\FaraVision\InputPassword.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Model\FaraVision\ModelWindow.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Model\FaraVision\SettingForm.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Model\FaraVision\newPrj.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="View\TVTestControl.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="WOCODEINPUTFORM.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\AT9620\AT9620.csproj">
      <Project>{e3ac0c35-18b7-4f15-8014-b07f7e64081a}</Project>
      <Name>AT9620</Name>
    </ProjectReference>
    <ProjectReference Include="..\Camera\Camera.csproj">
      <Project>{671e5d0c-031d-49eb-872e-c4940d6c921d}</Project>
      <Name>Camera</Name>
    </ProjectReference>
    <ProjectReference Include="..\HF800\Honeywell.csproj">
      <Project>{f9c8e8a3-77c6-4667-a3eb-ad898bc97df2}</Project>
      <Name>Honeywell</Name>
    </ProjectReference>
    <ProjectReference Include="..\PositionDetect\PositionDetect.csproj">
      <Project>{ac2ca99b-7a0b-4f36-ae44-23105cca4332}</Project>
      <Name>PositionDetect</Name>
    </ProjectReference>
    <ProjectReference Include="..\SQLITEDATABASE\SQLITEDATABASE.csproj">
      <Project>{2dc79245-e271-4d0a-aa9b-8eaed2bdc88e}</Project>
      <Name>SQLITEDATABASE</Name>
    </ProjectReference>
    <ProjectReference Include="..\TcpClientHelper\TcpClientHelper.csproj">
      <Project>{5dbf2327-fc16-4f70-8aaf-a9438b35742d}</Project>
      <Name>TcpClientHelper</Name>
    </ProjectReference>
    <ProjectReference Include="..\TcpServerHelper\TcpServerHelper.csproj">
      <Project>{ba0ae9a1-242b-4f42-a603-8a5693263902}</Project>
      <Name>TcpServerHelper</Name>
    </ProjectReference>
    <ProjectReference Include="..\最新测试方案20250506\MES_ORACLE_DATABASE-20240606\MES_ORACLE_DATABASE.csproj">
      <Project>{f1aabad4-eed4-4977-9154-b8c7e4ee8ce6}</Project>
      <Name>MES_ORACLE_DATABASE</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="halcon.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="hcanvas.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="..\packages\Fody.6.6.4\build\Fody.targets" Condition="Exists('..\packages\Fody.6.6.4\build\Fody.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Fody.6.6.4\build\Fody.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Fody.6.6.4\build\Fody.targets'))" />
    <Error Condition="!Exists('..\packages\PropertyChanged.Fody.4.1.0\build\PropertyChanged.Fody.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\PropertyChanged.Fody.4.1.0\build\PropertyChanged.Fody.props'))" />
  </Target>
</Project>