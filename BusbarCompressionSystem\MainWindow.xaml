﻿<p:WindowX x:Class="BusbarCompressionSystem.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:BusbarCompressionSystem"
        xmlns:control="clr-namespace:BusbarCompressionSystem.View"
        mc:Ignorable="d"
           Title="法拉电子-母排压合系统" Height="1080" Width="1920"
        p:WindowXCaption.Background="Orange"
        p:WindowXCaption.Foreground="White"
        p:WindowXCaption.Height="60"
        xmlns:halcon="clr-namespace:HalconDotNet;assembly=halcondotnet"
        WindowStartupLocation="CenterScreen" WindowState="Maximized"
            xmlns:ctr="clr-namespace:BusbarCompressionSystem.Model.FaraVision.Control"
        Loaded="WindowX_Loaded"
        Closing="WindowX_Closing"
        FontSize="12"
        xmlns:p="https://opensource.panuon.com/wpf-ui"
        DataContext="{Binding Source={StaticResource Locator},Path=Main.DataModel}"
       >
    <p:WindowXCaption.HeaderTemplate>
        <DataTemplate>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="auto"/>
                    <ColumnDefinition Width="auto"/>
                    <ColumnDefinition />
                    <ColumnDefinition Width="auto"/>
                    <ColumnDefinition Width="auto"/>
                    <ColumnDefinition Width="auto"/>
                    <ColumnDefinition Width="auto"/>
                    <ColumnDefinition Width="auto"/>
                    <ColumnDefinition Width="auto"/>
                </Grid.ColumnDefinitions>
                <!--<TextBlock VerticalAlignment="Center" Margin="30,0" FontSize="20">法拉电子-母排压合系统</TextBlock>-->
                <Menu p:WindowX.IsDragMoveArea="False" FontSize="20"   VerticalAlignment="Center" Margin="20,0,0,0" Background="Transparent" BorderThickness="0">
                    <MenuItem Header="法拉电子-母排压合系统" p:MenuItemHelper.HoverBackground="Transparent" Background="Transparent" Foreground="WhiteSmoke" BorderThickness="0">
                        <MenuItem Header="新建工程" FontSize="12" Padding="20,0" Background="WhiteSmoke" Foreground="#010101" p:MenuItemHelper.HoverForeground="WhiteSmoke" p:MenuItemHelper.HoverBackground="Orange"  Visibility="{Binding  Source={StaticResource Locator},Path=Main.DataModel.FaraVisionDataModel.Settingmodel.permission,Converter={StaticResource Bool2Visible_Converter}}" Command="{Binding Source={StaticResource Locator},Path=Main.NEW_PRJCMD}"/>
                        <MenuItem Header="保存工程" FontSize="12" Padding="20,0" Background="WhiteSmoke" Foreground="#010101" p:MenuItemHelper.HoverForeground="WhiteSmoke" p:MenuItemHelper.HoverBackground="Orange"                                                                                           Command="{Binding Source={StaticResource Locator},Path=Main.SAVE_PRJCMD}"/>
                        <!--<MenuItem Header="注册主控" FontSize="12" Padding="20,0" Background="WhiteSmoke" Foreground="#010101" p:MenuItemHelper.HoverForeground="WhiteSmoke" p:MenuItemHelper.HoverBackground="Orange"  Visibility="{Binding DATA.permission,Converter={StaticResource Bool2Visible_Converter}}" Command="{Binding Source={StaticResource Locator},Path=Main.REGISTER_MAIN_CMD}" />
                        <MenuItem Header="添加主控" FontSize="12" Padding="20,0" Background="WhiteSmoke" Foreground="#010101" p:MenuItemHelper.HoverForeground="WhiteSmoke" p:MenuItemHelper.HoverBackground="Orange"  Visibility="{Binding DATA.permission,Converter={StaticResource Bool2Visible_Converter}}" Command="{Binding Source={StaticResource Locator},Path=Main.ADD_MAIN_CMD}"/>-->
                        <!--<MenuItem Header="开始点检" FontSize="12" Padding="20,0" Background="WhiteSmoke" Foreground="#010101" p:MenuItemHelper.HoverForeground="WhiteSmoke" p:MenuItemHelper.HoverBackground="Orange"  Click="Button_Click_4"/>-->
                    </MenuItem>
                </Menu>


                <Button Grid.Column="6" p:WindowX.IsDragMoveArea="False" 
                        Margin="5" Height="30" Width="30" FontSize="18" 
                        FontFamily="{StaticResource PanuonIconFont}"
                        Background="OrangeRed" Foreground="White"
                        p:ButtonHelper.CornerRadius="15"
                       Content="&#xe98f;" Click="Button_Click_3" />
                <TextBlock Grid.Column="1" VerticalAlignment="Center" FontSize="20" 
                           Text="{Binding Source={StaticResource Locator},Path=Main.DataModel.Processmodel.PartNOID}"/>
                <Button p:WindowX.IsDragMoveArea="False" Grid.Column="8" Margin="5" p:ButtonHelper.CornerRadius="50" 
                        Background="{Binding Source={StaticResource Locator},Path=Main.DataModel.FaraVisionDataModel.Settingmodel.permission,Converter={StaticResource Bool2SolidColor_Converter}}" 
                   Foreground="WhiteSmoke"  Name="permissionbtn" Click="permissionbtn_Click"
                   Height="30" Width="30" FontSize="18"
                   FontFamily="{StaticResource PanuonIconFont}"
                   Content="{Binding Source={StaticResource Locator},Path=Main.DataModel.FaraVisionDataModel.Settingmodel.permission,Converter={StaticResource permission2lockorunlock_Converter }}" 
                   ToolTip="权限"
                    ></Button>

            </Grid>
        </DataTemplate>
    </p:WindowXCaption.HeaderTemplate>


    <Grid Margin="2">
        <Grid.RowDefinitions>
            <RowDefinition Height="auto"/>
            <RowDefinition/>
            <RowDefinition/>
            <RowDefinition/>
            <RowDefinition/>
            <RowDefinition Height="auto"/>
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition MaxWidth="400"/>
            <ColumnDefinition MaxWidth="300"/>
            <ColumnDefinition Width="2*"/>
        </Grid.ColumnDefinitions>
        <!--<Grid Margin="2" Grid.Row="0" Grid.Column="0">
            <Grid.RowDefinitions>
                <RowDefinition Height="auto"/>
                <RowDefinition/>
                <RowDefinition/>
                <RowDefinition/>
            </Grid.RowDefinitions>
            <TextBox p:TextBoxHelper.CornerRadius="15,0,0,15" Height="30" Margin="2,2,100,2" Padding="15,0"/>
            <Button p:ButtonHelper.CornerRadius="0,15,15,0" Height="30" HorizontalAlignment="Right" Width="100" Background="Black" Foreground="White">确定</Button>
        </Grid>-->

        <halcon:HSmartWindowControlWPF Grid.Row="1" Grid.Column="0" Margin="5" Name="Hwindow1" ></halcon:HSmartWindowControlWPF>
        <halcon:HSmartWindowControlWPF Grid.Row="2" Grid.Column="0" Margin="5" Name="Hwindow2" ></halcon:HSmartWindowControlWPF>
        <halcon:HSmartWindowControlWPF Grid.Row="3" Grid.Column="0" Margin="5" Name="Hwindow3" ></halcon:HSmartWindowControlWPF>
        <control:TVTestControl Grid.Row="1" Grid.Column="1" Margin="5" DataContext="{Binding Processmodel.TVTestTestModel1}" />
        <control:TVTestControl Grid.Row="2" Grid.Column="1" Margin="5" DataContext="{Binding Processmodel.TVTestTestModel2}" />
        <control:TVTestControl Grid.Row="3" Grid.Column="1" Margin="5" DataContext="{Binding Processmodel.TVTestTestModel3}" />


        <GroupBox Grid.Row="0" Grid.Column="0" Margin="2" Header="①扫码拍照" BorderBrush="Orange" p:GroupBoxHelper.HeaderForeground="Orange">
            <Grid  >
                <Grid.RowDefinitions>
                    <RowDefinition Height="auto"/>
                    <RowDefinition/>
                    <RowDefinition/>
                    <RowDefinition/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="auto"/>
                    <ColumnDefinition/>
                    <ColumnDefinition Width="auto"/>
                    <ColumnDefinition/>
                </Grid.ColumnDefinitions>


                <TextBox Grid.ColumnSpan="5" p:TextBoxHelper.Watermark="请输入编号" p:TextBoxHelper.CornerRadius="15,0,0,15" Height="30" Margin="2,2,100,2" Padding="15,0"
                     Text="{Binding Processmodel.sninputstr,UpdateSourceTrigger=PropertyChanged}"
                      KeyDown="TextBox_KeyDown"
                     />
                <Button Grid.ColumnSpan="5" p:ButtonHelper.CornerRadius="0,15,15,0" Height="30" HorizontalAlignment="Right" Width="100" Background="Black" Foreground="White" Click="Button_Click">确定</Button>
                <TextBlock Grid.Row="1" Grid.Column="0"  Height="30" Margin="2" >批号:</TextBlock>
                <TextBlock Grid.Row="2" Grid.Column="0"  Height="30" Margin="2" >编号:</TextBlock>
                <TextBlock Grid.Row="3" Grid.Column="0"  Height="30" Margin="2" >错误信息:</TextBlock>

                <TextBlock Grid.Row="1" Grid.Column="1"  Height="30" Margin="2"  Text="{Binding Processmodel.TakePhotoTestModel.Productinfo.SN}"/>
                <TextBlock Grid.Row="2" Grid.Column="1"  Height="30" Margin="2"  Text="{Binding Processmodel.TakePhotoTestModel.Productinfo.WOCODE}"/>

                <TextBlock Grid.Row="1" Grid.Column="2"  Height="30" Margin="2" >拍照留底结果:</TextBlock>
                <TextBox Grid.Row="3" Grid.Column="1" Grid.ColumnSpan="3" Margin="3" Text="{Binding  Processmodel.TakePhotoTestModel.error}"/>


            </Grid>

        </GroupBox>


        <GroupBox Grid.Row="0" Grid.Column="1" Margin="2" Header="②耐压参数" BorderBrush="Green" p:GroupBoxHelper.HeaderForeground="Green">
            <Grid  MinHeight="150">
                <Grid.RowDefinitions>
                    <RowDefinition Height="auto"/>
                    <RowDefinition/>
                    <RowDefinition/>
                    <RowDefinition/>
                    <RowDefinition/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="auto"/>
                    <ColumnDefinition/>
                    <ColumnDefinition Width="auto"/>
                    <ColumnDefinition Width="auto"/>
                    <ColumnDefinition/>
                    <ColumnDefinition Width="auto"/>
                </Grid.ColumnDefinitions>
                <TextBlock Grid.Row="0" Grid.Column="0"  Height="30" Margin="2" HorizontalAlignment="Right" >电压:</TextBlock>
                <TextBlock Grid.Row="1" Grid.Column="0"  Height="30" Margin="2" HorizontalAlignment="Right">缓升时间:</TextBlock>
                <TextBlock Grid.Row="2" Grid.Column="0"  Height="30" Margin="2" HorizontalAlignment="Right">上限:</TextBlock>
                <TextBlock Grid.Row="3" Grid.Column="0"  Height="30" Margin="2" HorizontalAlignment="Right">下限:</TextBlock>
                <TextBlock Grid.Row="4" Grid.Column="0"  Height="30" Margin="2" HorizontalAlignment="Right">频率:</TextBlock>

                <TextBlock Grid.Row="0" Grid.Column="3"  Height="30" Margin="2" HorizontalAlignment="Right">模式:</TextBlock>
                <TextBlock Grid.Row="1" Grid.Column="3"  Height="30" Margin="2" HorizontalAlignment="Right">测试时间:</TextBlock>
                <TextBlock Grid.Row="2" Grid.Column="3"  Height="30" Margin="2" HorizontalAlignment="Right">缓降时间:</TextBlock>
                <TextBlock Grid.Row="3" Grid.Column="3"  Height="30" Margin="2" HorizontalAlignment="Right">电弧等级:</TextBlock>
                <!--<TextBlock Grid.Row="4" Grid.Column="3"  Height="30" Margin="2" >充电下限:</TextBlock>-->

                <TextBlock Grid.Row="0" Grid.Column="1"  Height="30" Margin="2" Text="{Binding Processmodel.TVParameter.Voltage}" />
                <TextBlock Grid.Row="1" Grid.Column="1"  Height="30" Margin="2" Text="{Binding Processmodel.TVParameter.RiseTime}"/>
                <TextBlock Grid.Row="2" Grid.Column="1"  Height="30" Margin="2" Text="{Binding Processmodel.TVParameter.High}"/>
                <TextBlock Grid.Row="3" Grid.Column="1"  Height="30" Margin="2" Text="{Binding Processmodel.TVParameter.Low}"/>
                <TextBlock Grid.Row="4" Grid.Column="1"  Height="30" Margin="2" Text="{Binding Processmodel.TVParameter.Freq}"/>

                <TextBlock Grid.Row="0" Grid.Column="4"  Height="30" Margin="2" Text="{Binding Processmodel.TVParameter.TestMode}"/>
                <TextBlock Grid.Row="1" Grid.Column="4"  Height="30" Margin="2" Text="{Binding Processmodel.TVParameter.TestTime}"/>
                <TextBlock Grid.Row="2" Grid.Column="4"  Height="30" Margin="2" Text="{Binding Processmodel.TVParameter.FallTime}"/>
                <TextBlock Grid.Row="3" Grid.Column="4"  Height="30" Margin="2" Text="{Binding Processmodel.TVParameter.Arc}"/>


                <!--<TextBlock Grid.Row="0" Grid.Column="2"  Height="30" Margin="2" >V</TextBlock>
                <TextBlock Grid.Row="1" Grid.Column="2"  Height="30" Margin="2" >s</TextBlock>
                <TextBlock Grid.Row="2" Grid.Column="2"  Height="30" Margin="2" >mA</TextBlock>
                <TextBlock Grid.Row="3" Grid.Column="2"  Height="30" Margin="2" >mA</TextBlock>
                -->
                <!--<TextBlock Grid.Row="4" Grid.Column="2"  Height="30" Margin="2" >频率:</TextBlock>-->

                <!--<TextBlock Grid.Row="0" Grid.Column="5"  Height="30" Margin="2" >模式:</TextBlock>-->
                <!--
                <TextBlock Grid.Row="1" Grid.Column="5"  Height="30" Margin="2" >s</TextBlock>
                <TextBlock Grid.Row="2" Grid.Column="5"  Height="30" Margin="2" >s</TextBlock>
                -->
                <!--<TextBlock Grid.Row="3" Grid.Column="5"  Height="30" Margin="2" >电弧等级:</TextBlock>-->
            </Grid>

        </GroupBox>
        <GroupBox Grid.Row="0" Grid.Column="2" Header="③外观检测" Grid.RowSpan="4" Margin="2" BorderBrush="Purple" p:GroupBoxHelper.HeaderForeground="Purple">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="auto"/>
                    <RowDefinition/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="auto"/>
                    <ColumnDefinition/>
                    <ColumnDefinition Width="auto"/>
                    <ColumnDefinition/>
                </Grid.ColumnDefinitions>
                <Border  Grid.Row="1" Grid.Column="0" Margin="5" Grid.ColumnSpan="6" BorderBrush="Gray" BorderThickness="1" >
                    <Grid Margin="3">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="auto"/>
                            <RowDefinition Height="auto" MaxHeight="160" MinHeight="100"/>
                            <RowDefinition Height="auto" />
                            <RowDefinition/>
                            <RowDefinition Height="auto" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition/>
                            <ColumnDefinition Width="auto" />
                            <ColumnDefinition Width="auto" />
                        </Grid.ColumnDefinitions>
                        <Grid Grid.Row="0" Grid.ColumnSpan="2" MinHeight="30" Margin="5" >
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="auto"/>
                                <ColumnDefinition />
                                <ColumnDefinition Width="auto" MinWidth="100"/>
                                <ColumnDefinition Width="auto" />
                                <ColumnDefinition Width="auto" />
                                <ColumnDefinition Width="auto" />
                                <ColumnDefinition Width="auto" />
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" VerticalAlignment="Center" Margin="5">工程名称</TextBlock>
                            <ComboBox Grid.Column="1" IsEditable="False" SelectedIndex="{Binding FaraVisionDataModel.Settingmodel .prjselected,UpdateSourceTrigger=PropertyChanged}"  p:ComboBoxHelper.CornerRadius="15,0,0,15" Padding="15,0" ItemsSource="{Binding FaraVisionDataModel.Settingmodel.Prjs}" />
                            <Button Grid.Column="2" x:Name="ChangePrj" p:ButtonHelper.CornerRadius="0,15,15,0" Background="#101010" Foreground="WhiteSmoke"  Click="ChangePrj_Click" >切换工程</Button>


                        </Grid>

                        <ListBox Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="2" p:ListBoxHelper.CornerRadius="5"
                 BorderBrush="#55222222" Background="Gray"
                 p:ListBoxHelper.ItemsSelectedBackground="#55FF0000" 
                 SelectionChanged="ListBox_SelectionChanged"
                 SelectedIndex="{Binding FaraVisionDataModel.Processmodel.selectedindex}"  
                 MouseDoubleClick="ListBox_MouseDoubleClick" Margin="5,0,5,0"  
                 ItemsSource="{Binding FaraVisionDataModel.Processmodel.Tools}" 
                 >
                            <ListBox.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <StackPanel  Orientation="Horizontal"/>
                                </ItemsPanelTemplate>
                            </ListBox.ItemsPanel>
                            <ListBox.ItemTemplate>
                                <DataTemplate>
                                    <ctr:Toolcontrol MinHeight="{Binding Source={StaticResource Locator},Path=Main.DataModel.FaraVisionDataModel.Settingmodel.ImageSize}" 
                                     MinWidth="{Binding Source={StaticResource Locator},Path=Main.DataModel.FaraVisionDataModel.Settingmodel.ImageSize}" 
                                     DataContext="{Binding}"/>
                                </DataTemplate>
                            </ListBox.ItemTemplate>
                        </ListBox>
                        <Grid Grid.Row="1" Grid.Column="2" 
                              IsEnabled="{Binding DataModel. FaraVisionDataModel.Settingmodel.permission }"
                              DataContext="{Binding Source={StaticResource Locator},Path=Main}"
                              >
                          
                            <!--<Grid Grid.Row="1" Grid.Column="2" Visibility="{Binding DataModel.Settingmodel.permission ,Converter={StaticResource Bool2Visible_Converter}}" >-->
                            <Grid.RowDefinitions>
                                <RowDefinition/>
                                <RowDefinition/>
                                <RowDefinition/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition/>
                                <ColumnDefinition/>
                            </Grid.ColumnDefinitions>
                            <Button   Grid.Row="0" Grid.Column="0"   Background="#101010" Foreground="White"  Command="{Binding AddToolCMD}"    p:ButtonHelper.CornerRadius="15,0,0,0">追加</Button>
                            <Button   Grid.Row="0" Grid.Column="1"   Background="#101010" Foreground="White"  Command="{Binding InsertToolCMD}" >插入</Button>
                            <Button   Grid.Row="1" Grid.Column="0"   Background="#101010" Foreground="White"  Command="{Binding CopyToolCMD}" Grid.ColumnSpan="2"  >复制</Button>
                            <Button   Grid.Row="2" Grid.Column="0"   Background="#101010" Foreground="White"  Command="{Binding DeleteToolCMD}" >删除</Button>
                            <Button   Grid.Row="2" Grid.Column="1"   Background="#101010" Foreground="White"  Command="{Binding ClearToolCMD}"  p:ButtonHelper.CornerRadius="0,0,15,0">清空</Button>
                        </Grid>
                        <Grid Grid.Row="3" Grid.Column="1" Grid.ColumnSpan="2" >
                            <Grid.RowDefinitions>
                                <RowDefinition Height="auto"/>
                                <RowDefinition Height="auto"/>
                                <RowDefinition/>
                                <RowDefinition Height="auto"/>
                            </Grid.RowDefinitions>

                            <GroupBox>
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="auto"/>
                                        <RowDefinition Height="auto"/>
                                        <RowDefinition Height="auto"/>
                                        <RowDefinition Height="auto"/>
                                    </Grid.RowDefinitions>

                                    <WrapPanel>
                                        <TextBlock Text="{Binding FaraVisionDataModel.Processmodel.tool.Index}"/>
                                        <TextBlock Text="{Binding FaraVisionDataModel.Processmodel.tool.Name}"/>
                                    </WrapPanel>
                                    <Grid Grid.Row="1"   Visibility="{Binding FaraVisionDataModel.Processmodel.tool.TestMode,Converter={StaticResource TestMode2Visibility_Barcode}}">
                                        <Grid.RowDefinitions>
                                            <RowDefinition/>
                                            <RowDefinition/>
                                            <RowDefinition/>
                                            <RowDefinition/>
                                            <RowDefinition/>
                                            <RowDefinition/>
                                        </Grid.RowDefinitions>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="auto"/>
                                            <ColumnDefinition/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Row="0" Grid.Column="0" VerticalAlignment="Center" HorizontalAlignment="Right" Visibility="{Binding FaraVisionDataModel.Processmodel.tool.TestMode,Converter={StaticResource TestMode2Visibility_Barcode}}">二维码内容:</TextBlock>

                                        <TextBlock Grid.Row="0" Grid.Column="1" VerticalAlignment="Center" HorizontalAlignment="Center" Text="{Binding FaraVisionDataModel.Processmodel.tool.BarcodeStr}" />
                                    </Grid>

                                    <Grid Grid.Row="2"  Visibility="{Binding FaraVisionDataModel.Processmodel.tool.TestMode,Converter={StaticResource TestMode2Visibility_ShapeMatch}}">
                                        <Grid.RowDefinitions>
                                            <RowDefinition/>
                                            <RowDefinition/>
                                            <RowDefinition/>
                                            <RowDefinition/>
                                            <RowDefinition/>
                                            <RowDefinition/>
                                        </Grid.RowDefinitions>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="auto"/>
                                            <ColumnDefinition/>
                                            <ColumnDefinition Width="auto"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Row="0" Grid.Column="0" VerticalAlignment="Center" HorizontalAlignment="Right">X坐标:</TextBlock>
                                        <TextBlock Grid.Row="1" Grid.Column="0" VerticalAlignment="Center" HorizontalAlignment="Right">Y坐标:</TextBlock>
                                        <TextBlock Grid.Row="2" Grid.Column="0" VerticalAlignment="Center" HorizontalAlignment="Right">X偏移:</TextBlock>
                                        <TextBlock Grid.Row="3" Grid.Column="0" VerticalAlignment="Center" HorizontalAlignment="Right">Y偏移:</TextBlock>
                                        <TextBlock Grid.Row="4" Grid.Column="0" VerticalAlignment="Center" HorizontalAlignment="Right">角度:</TextBlock>
                                        <TextBlock Grid.Row="5" Grid.Column="0" VerticalAlignment="Center" HorizontalAlignment="Right">分值:</TextBlock>

                                        <TextBlock Grid.Row="0" Grid.Column="1" VerticalAlignment="Center" HorizontalAlignment="Center" Text="{Binding FaraVisionDataModel.Processmodel.tool.ActualX,StringFormat={}{0:F1}}"/>
                                        <TextBlock Grid.Row="1" Grid.Column="1" VerticalAlignment="Center" HorizontalAlignment="Center" Text="{Binding FaraVisionDataModel.Processmodel.tool.ActualY,StringFormat={}{0:F1}}"/>
                                        <TextBlock Grid.Row="2" Grid.Column="1" VerticalAlignment="Center" HorizontalAlignment="Center" Text="{Binding FaraVisionDataModel.Processmodel.tool.DeltaX,StringFormat={}{0:F4}}"/>
                                        <TextBlock Grid.Row="3" Grid.Column="1" VerticalAlignment="Center" HorizontalAlignment="Center" Text="{Binding FaraVisionDataModel.Processmodel.tool.DeltaY,StringFormat={}{0:F4}}"/>
                                        <TextBlock Grid.Row="4" Grid.Column="1" VerticalAlignment="Center" HorizontalAlignment="Center" Text="{Binding FaraVisionDataModel.Processmodel.tool.ActualAngle,StringFormat={}{0:F3}}"/>
                                        <TextBlock Grid.Row="5" Grid.Column="1" VerticalAlignment="Center" HorizontalAlignment="Center" Text="{Binding FaraVisionDataModel.Processmodel.tool.ActualScore,StringFormat={}{0:F3}}"/>


                                        <TextBlock Grid.Row="0" Grid.Column="2" VerticalAlignment="Center" HorizontalAlignment="Right" Visibility="Collapsed">pixel</TextBlock>
                                        <TextBlock Grid.Row="1" Grid.Column="2" VerticalAlignment="Center" HorizontalAlignment="Right" Visibility="Collapsed">pixel</TextBlock>
                                        <!--<TextBlock Grid.Row="2" Grid.Column="2" VerticalAlignment="Center" HorizontalAlignment="Right">mm</TextBlock>-->
                                        <!--<TextBlock Grid.Row="3" Grid.Column="2" VerticalAlignment="Center" HorizontalAlignment="Right">mm</TextBlock>-->

                                    </Grid>
                                    <Grid Grid.Row="2"  Visibility="{Binding FaraVisionDataModel.Processmodel.tool.TestMode,Converter={StaticResource TestMode2Visibility_Dimension}}">
                                        <Grid.RowDefinitions>
                                            <RowDefinition/>
                                            <RowDefinition/>
                                            <RowDefinition/>
                                            <RowDefinition/>
                                            <RowDefinition/>
                                        </Grid.RowDefinitions>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="auto"/>
                                            <ColumnDefinition/>
                                            <ColumnDefinition Width="auto"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Row="0" Grid.Column="0" VerticalAlignment="Center" HorizontalAlignment="Right">实际面积:</TextBlock>

                                        <TextBlock Grid.Row="0" Grid.Column="1" VerticalAlignment="Center" HorizontalAlignment="Center" Text="{Binding FaraVisionDataModel.Processmodel.tool.ActualDimension,StringFormat={}{0:F1}}"/>




                                    </Grid>


                                </Grid>
                            </GroupBox>
                            <GroupBox Header="累积二维码" Grid.Row="1">
                                <TextBox IsEnabled="False" FontSize="12" TextWrapping="Wrap" MaxWidth="400" MinWidth="300" MinHeight="100"  Text="{Binding FaraVisionDataModel.Processmodel.Barcodes}" />


                            </GroupBox>
                            <DataGrid ItemsSource="{Binding FaraVisionDataModel.Recordmodel.workLog}" AutoGenerateColumns="False" Grid.Row="2" >
                                <DataGrid.Columns>
                                    <DataGridTextColumn Binding="{Binding}" FontSize="12" />
                                </DataGrid.Columns>
                            </DataGrid>

                            <ListBox ItemsSource="{Binding FaraVisionDataModel.Processmodel.SNList}" Grid.Row="3" MinHeight="40" Padding="5" Margin="2">

                            </ListBox>
                        </Grid>

                        <ListBox Grid.Row="2"  Grid.Column="0" Grid.ColumnSpan="2" 
                 p:ListBoxHelper.CornerRadius="5" HorizontalAlignment="Center" 
                 VerticalAlignment="Center"
                  Background="White" BorderThickness="0"
                 p:ListBoxHelper.ItemsSelectedBackground="#55FF0000" 
                 SelectionChanged="ListBox_SelectionChanged"
                 SelectedIndex="{Binding FaraVisionDataModel.Processmodel.selectedindex}"  
                 MouseDoubleClick="ListBox_MouseDoubleClick" Margin="5,0,5,0"  
                 ItemsSource="{Binding FaraVisionDataModel.Processmodel.Tools}" >
                            <ListBox.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <StackPanel  Orientation="Horizontal"/>
                                </ItemsPanelTemplate>
                            </ListBox.ItemsPanel>
                            <ListBox.ItemTemplate>
                                <DataTemplate>
                                    <!--<ctr:Toolcontrol MinHeight="140" MinWidth="140" DataContext="{Binding}"/>-->
                                    <!--<Rectangle Width="10" Height="10" />-->
                                    <Grid DataContext="{Binding}">
                                        <TextBlock  Background="{Binding StatusColor}" Text="{Binding Index,StringFormat={}{0:00}}" Padding="2">
                                            <TextBlock.ToolTip>
                                                <StackPanel Grid.Row="1" HorizontalAlignment="Center" VerticalAlignment="Bottom" Orientation="Vertical" >
                                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                                        <TextBlock    Foreground="Orange" Text="{Binding Index }"/>
                                                        <TextBlock    Foreground="DarkBlue" Text="{Binding Name }" Margin="0,0,10,0"/>
                                                        <TextBlock HorizontalAlignment="Center">模板照片</TextBlock>
                                                    </StackPanel>
                                                    <Image   HorizontalAlignment="Center" Source="{Binding BitmapSource}"/>
                                                </StackPanel>
                                            </TextBlock.ToolTip>
                        </TextBlock>
                                    </Grid>
                                </DataTemplate>
                            </ListBox.ItemTemplate>

                        </ListBox>
                        <halcon:HSmartWindowControlWPF Grid.Row="3" Grid.Column="0" Margin="5"  Name="Hwindow4" ></halcon:HSmartWindowControlWPF>
                        <TextBlock Grid.Row="2" Grid.Column="0"  Text="{Binding FaraVisionDataModel.Processmodel.Status}" 
                   HorizontalAlignment="Left"
                   Margin="20,5,5,5"
                   FontSize="20"
                   Foreground="{Binding  FaraVisionDataModel.Processmodel.StatusColor }"
                   
                   />
                        <WrapPanel Grid.Row="4" HorizontalAlignment="Center" Grid.ColumnSpan="3" >

                            <TextBlock Margin="10,0" Text="{Binding FaraVisionDataModel.Processmodel.ToolIndex,StringFormat=工具编号:{0}}"/>
                            <TextBlock Margin="10,0" Text="{Binding FaraVisionDataModel.Processmodel.RCMD,StringFormat=接收:{0}}"/>
                            <TextBlock Margin="10,0" Text="{Binding FaraVisionDataModel.Processmodel.Trig_IO.IOstatus,StringFormat=触发:{0}}" 
                       Background="{Binding FaraVisionDataModel.Processmodel.Trig_IO.IOstatusBackGround}" 
                       Foreground="{Binding FaraVisionDataModel.Processmodel.Trig_IO.IOStatusForeGround}"
                       Padding="10,2"
                       />
                            <Button Name="tooltest" Click="tooltest_Click" Content="照片测试" />

                        </WrapPanel>

                    </Grid>



                </Border>

                <TextBlock Grid.Row="0" Grid.Column="0">批号:</TextBlock>
                <TextBlock Grid.Row="0" Grid.Column="2">编号:</TextBlock>

                <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding Processmodel.TakePhotoTestMode2.Productinfo.WOCODE}"/>
                <TextBlock Grid.Row="0" Grid.Column="3" Text="{Binding  Processmodel.TakePhotoTestMode2.Productinfo.SN}"/>

            </Grid>
        </GroupBox>

        <WrapPanel Grid.Row="5" Grid.ColumnSpan="5">
            <Button Click="Button_Click_2">耐压1参数下发</Button>
            <Button Click="Button_Click_1">开始耐压1测试</Button>
            <TextBlock Text="{Binding Processmodel.Scan_Trig_IO.IOstatus}"  Background="{Binding Processmodel.Scan_Trig_IO.IOstatusBackGround}" Foreground="{Binding Processmodel.Scan_Trig_IO.IOStatusForeGround}" Padding="10,2" ToolTip="扫码触发信号" />
            <TextBlock Text="{Binding Processmodel.TakePhoto1_Trig_IO.IOstatus}"  Background="{Binding Processmodel.TakePhoto1_Trig_IO.IOstatusBackGround}" Foreground="{Binding Processmodel.TakePhoto1_Trig_IO.IOStatusForeGround}" Padding="10,2" ToolTip="拍照留底触发信号" />
            <TextBlock Text="{Binding Processmodel.TV1_Trig_IO.IOstatus}"  Background="{Binding Processmodel.TV1_Trig_IO.IOstatusBackGround}" Foreground="{Binding Processmodel.TV1_Trig_IO.IOStatusForeGround}" Padding="10,2" ToolTip="耐压1触发信号"/>
            <TextBlock Text="{Binding Processmodel.TV2_Trig_IO.IOstatus}"  Background="{Binding Processmodel.TV2_Trig_IO.IOstatusBackGround}" Foreground="{Binding Processmodel.TV2_Trig_IO.IOStatusForeGround}" Padding="10,2" ToolTip="耐压2触发信号"/>
            <TextBlock Text="{Binding Processmodel.TV3_Trig_IO.IOstatus}"  Background="{Binding Processmodel.TV3_Trig_IO.IOstatusBackGround}" Foreground="{Binding Processmodel.TV3_Trig_IO.IOStatusForeGround}" Padding="10,2" ToolTip="耐压3触发信号"/>


            <TextBlock Text="{Binding Processmodel.CMD,StringFormat=命令:{0}}" Margin="20,0"/>

            <Button x:Name="check1" Click="check1_Click" Visibility="Collapsed">check1</Button>

        </WrapPanel>

        <Grid Grid.Row="4" Grid.ColumnSpan="5">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <DataGrid Grid.Column="0" AutoGenerateColumns="False" CanUserAddRows="False" CanUserDeleteRows="False" CanUserReorderColumns="False" CanUserResizeRows="False" CanUserSortColumns="False"
                      ItemsSource="{Binding Recordmodel.ProductInfoRecords}" IsReadOnly="True" VerticalContentAlignment="Center"
                      >
                <DataGrid.ContextMenu>
                    <ContextMenu>
                        <MenuItem Click="MenuItem_Click">清空数据</MenuItem>
                        <MenuItem Click="MenuItem_Click_1">保留十行数据</MenuItem>
                    </ContextMenu>
                </DataGrid.ContextMenu>
                <DataGrid.Columns>
                    <DataGridTextColumn Header="规格" Binding="{Binding Productinfo.PartNOID}"></DataGridTextColumn>
                    <DataGridTextColumn Header="批号" Binding="{Binding Productinfo.WOCODE}"></DataGridTextColumn>
                    <DataGridTextColumn Header="编号" Binding="{Binding Productinfo.SN}"></DataGridTextColumn>
                    <DataGridTextColumn Header="拍照留底" Binding="{Binding TakePhoto1}"></DataGridTextColumn>
                    <DataGridTextColumn Header="TV结果" Binding="{Binding TVResult}"></DataGridTextColumn>
                    <!--<DataGridTextColumn Header="TV最大电流" Binding="{Binding TVMaxVoltage}"></DataGridTextColumn>-->
                    <DataGridTextColumn Header="阻值" Binding="{Binding Res}"></DataGridTextColumn>
                    <DataGridTextColumn Header="视觉检测结果" Binding="{Binding AppearanceInspection}"></DataGridTextColumn>
                    <DataGridTextColumn Header="时间" Binding="{Binding DateTime,StringFormat={}{0:yyyy-MM-dd HH:mm:ss.FFF}}"></DataGridTextColumn>
                </DataGrid.Columns>
            </DataGrid>

            <DataGrid Grid.Column="1" AutoGenerateColumns="False" CanUserAddRows="False" CanUserDeleteRows="False" CanUserReorderColumns="False" CanUserResizeRows="False" CanUserSortColumns="False" 
                      ItemsSource="{Binding Recordmodel.workLog}" IsReadOnly="True" VerticalContentAlignment="Center"
                      >
                <DataGrid.Columns>
                    <DataGridTextColumn Header="时间" Binding="{Binding DateTime,StringFormat={}{0:yyyy-MM-dd HH:mm:ss.FFF} }"></DataGridTextColumn>
                    <DataGridTextColumn Header="信息" Binding="{Binding log}"></DataGridTextColumn>
                </DataGrid.Columns>
            </DataGrid>
        </Grid>
    </Grid>
</p:WindowX>
