﻿<UserControl x:Class="BusbarCompressionSystem.Model.FaraVision.Control.Toolcontrol"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:BusbarCompressionSystem.Model.FaraVision.Control"
             mc:Ignorable="d" 
            d:DesignHeight="{Binding Source={StaticResource Locator},Path=Main.DataModel.FaraVisionDataModel.Settingmodel.ImageSize}"
            d:DesignWidth="{Binding Source={StaticResource Locator},Path=Main.DataModel.FaraVisionDataModel.Settingmodel.ImageSize}"
             Height="{Binding Source={StaticResource Locator},Path=Main.DataModel.FaraVisionDataModel.Settingmodel.ImageSize}"
             Width="{Binding Source={StaticResource Locator},Path=Main.DataModel.FaraVisionDataModel.Settingmodel.ImageSize}"
               
             >
    <!--<UserControl.DataContext>
        <Binding Source="{Binding Source={StaticResource Locator}}" Path="Main.DataModel.Processmodel.tool"/>
    </UserControl.DataContext>-->
    <Grid>

        <Grid.RowDefinitions>
            <RowDefinition/>
            <RowDefinition Height="auto"/>
        </Grid.RowDefinitions>
        <Border Margin="2" BorderThickness="1" BorderBrush="Orange" CornerRadius="10" Background="#99FFFFFF"  >
            <Border.ToolTip>
                <StackPanel Grid.Row="1" HorizontalAlignment="Center" VerticalAlignment="Bottom" Orientation="Vertical" >
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                        <TextBlock    Foreground="Orange" Text="{Binding Index }"/>
                        <TextBlock    Foreground="DarkBlue" Text="{Binding Name }" Margin="0,0,10,0"/>
                        <TextBlock HorizontalAlignment="Center">模板照片</TextBlock>
                    </StackPanel>
                    <Image   HorizontalAlignment="Center" Source="{Binding BitmapSource}"/>
                </StackPanel>
            </Border.ToolTip>

            <Image Margin="0" Source="{Binding  CurrentBitmapSource}"/>
        </Border>
        <!--<Border Margin="2" BorderThickness="1" BorderBrush="Orange" CornerRadius="10" Background="#99FFFFFF"  Visibility="Collapsed">
            <Image Margin="0" Source="{Binding  CurrentBitmapSource}" />
        </Border>-->
        <TextBlock Foreground="{Binding StatusColor}" Text="{Binding TestMode}" VerticalAlignment="Top" HorizontalAlignment="Left"  Margin="25,5,0,0" FontSize="12" />
        <Border BorderThickness="0.5" BorderBrush="Black" Background="{Binding StatusColor}" CornerRadius="5" Width="10" Height="10" Margin="5" HorizontalAlignment="Left" VerticalAlignment="Top"></Border>
        <TextBlock Foreground="{Binding StatusColor}" Text="{Binding  ToolStatus}" VerticalAlignment="Top" HorizontalAlignment="Right" Margin="5" FontSize="12" />
        <StackPanel Grid.Row="1" HorizontalAlignment="Center" VerticalAlignment="Bottom" Orientation="Horizontal" >
            <TextBlock   VerticalAlignment="Center" Foreground="Orange" Text="{Binding Index }"/>
            <TextBlock VerticalAlignment="Center"   Foreground="DarkBlue" Text="{Binding Name }"/>
        </StackPanel>
    </Grid>
</UserControl>
