﻿<p:WindowX x:Class="BusbarCompressionSystem.Model.FaraVision.InputPassword"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:BusbarCompressionSystem.Model.FaraVision"
       mc:Ignorable="d"
             xmlns:p="clr-namespace:Panuon.WPF.UI;assembly=Panuon.WPF.UI"
           WindowStartupLocation="CenterScreen"
           FocusManager.FocusedElement="{Binding ElementName=passwordbox}"
           p:WindowXCaption.Height="30" p:WindowXCaption.Background="Orange" p:WindowXCaption.Foreground="WhiteSmoke"
        Title="输入密码" Height="150" Width="250">
    <!--<p:WindowXCaption.MinimizeButtonStyle>
        <Style TargetType="Button"
               BasedOn="{StaticResource {ComponentResourceKey ResourceId=MinimizeButtonStyle, TypeInTargetAssembly={x:Type p:WindowXCaption}}}">
            <Setter Property="VerticalAlignment"
                    Value="Center" />
            <Setter Property="Background"
                    Value="{x:Null}" />
            <Setter Property="Foreground"
                    Value="#E8E8E8" />
            <Setter Property="p:ButtonHelper.HoverForeground"
                    Value="#E1E1E1" />
            <Setter Property="p:ButtonHelper.HoverBackground"
                    Value="#99999999" />
        </Style>
    </p:WindowXCaption.MinimizeButtonStyle>
    <p:WindowXCaption.MaximizeButtonStyle>
        <Style TargetType="Button"
               BasedOn="{StaticResource {ComponentResourceKey ResourceId=MaximizeButtonStyle  , TypeInTargetAssembly={x:Type p:WindowXCaption}}}">
            <Setter Property="VerticalAlignment"
                    Value="Center" />
            <Setter Property="Background"
                    Value="{x:Null}" />
            <Setter Property="Foreground"
                    Value="#E8E8E8" />
            <Setter Property="p:ButtonHelper.HoverForeground"
                    Value="#E1E1E1" />
            <Setter Property="p:ButtonHelper.HoverBackground"
                    Value="#99999999" />
        </Style>
    </p:WindowXCaption.MaximizeButtonStyle>
    <p:WindowXCaption.CloseButtonStyle>
        <Style TargetType="Button"
               BasedOn="{StaticResource {ComponentResourceKey ResourceId=CloseButtonStyle, TypeInTargetAssembly={x:Type p:WindowXCaption}}}">
            <Setter Property="VerticalAlignment"
                    Value="Center" />
            <Setter Property="Background"
                    Value="#88D7000F" />
            <Setter Property="Foreground"
                    Value="#E8E8E8" />
            <Setter Property="p:ButtonHelper.HoverForeground"
                    Value="#E1E1E1" />
            <Setter Property="p:ButtonHelper.HoverBackground"
                    Value="#FFD7000F" />
        </Style>
    </p:WindowXCaption.CloseButtonStyle>-->
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition/>
            <RowDefinition/>
            <RowDefinition/>
        </Grid.RowDefinitions>
        <TextBlock Grid.Row="0" HorizontalAlignment="Center" VerticalAlignment="Center" >请输入密码</TextBlock>
        <PasswordBox x:Name="passwordbox" Grid.Row="1" BorderThickness="0,0,0,1" BorderBrush="Black" Margin="10,2" HorizontalContentAlignment="Center" KeyDown="passwordbox_KeyDown" />
        <StackPanel Grid.Row="2" HorizontalAlignment="Center" Orientation="Horizontal">
            <Button Click="Button_Click" Margin="10,5" MinWidth="100" p:ButtonHelper.CornerRadius="15" Height="30" Background="#fd433a" Foreground="WhiteSmoke">确定</Button>
            <Button Click="Button_Click_1" Margin="10,5" MinWidth="100" p:ButtonHelper.CornerRadius="15" Height="30">取消</Button>
        </StackPanel>
    </Grid>
</p:WindowX>
