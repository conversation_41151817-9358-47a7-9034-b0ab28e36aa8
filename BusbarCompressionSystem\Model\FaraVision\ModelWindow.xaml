﻿<p:WindowX x:Class="BusbarCompressionSystem.Model.FaraVision.ModelWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:BusbarCompressionSystem.Model.FaraVision"
        xmlns:p="clr-namespace:Panuon.WPF.UI;assembly=Panuon.WPF.UI"
        p:WindowXCaption.Background="#666666"
        p:WindowXCaption.Height="45"  
        WindowStartupLocation="CenterScreen"
           Closing="WindowX_Closing"
       p:WindowXCaption.Foreground="WhiteSmoke"
            xmlns:ctr="clr-namespace:PositionDetect;assembly=PositionDetect"
        Title="模型设置文件" Height="450" Width="800">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition/>
            <RowDefinition Height="auto"/>
        </Grid.RowDefinitions>
        <ctr:UserControl1 Grid.Row="0" x:Name="userctr" DataContext="{Binding Source={StaticResource Locator},Path=PositionDetectViewModel}" />
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition/>
                <ColumnDefinition/>
                <ColumnDefinition/>
            </Grid.ColumnDefinitions>
            <Button Grid.Column="0"  Margin="5" Padding="5" Background="OrangeRed" Foreground="White" p:ButtonHelper.CornerRadius="10" Name="loadshmfromfile" Click="loadshmfromfile_Click">重载模型</Button>
            <Button Grid.Column="1"  Margin="5" Padding="5" Background="Orange" Foreground="White" p:ButtonHelper.CornerRadius="10" Name="getinitpositionbymodelimage" Click="getinitpositionbymodelimage_Click">从模板提取基准点</Button>
            <Button Grid.Column="2"  Margin="5" Padding="5" Background="Black"  Foreground="White" p:ButtonHelper.CornerRadius="10" Name="getinitpositonfromimagefile" Click="getinitpositonfromimagefile_Click">从图片提取基准点</Button>

        </Grid>
    </Grid>
</p:WindowX>
