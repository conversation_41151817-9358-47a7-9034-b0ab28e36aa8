﻿<p:WindowX x:Class="BusbarCompressionSystem.Model.FaraVision.SettingForm"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:BusbarCompressionSystem.Model.FaraVision"
        xmlns:p="https://opensource.panuon.com/wpf-ui"
        xmlns:sys="clr-namespace:System;assembly=mscorlib"
        xmlns:faravision="clr-namespace:BusbarCompressionSystem.Model.FaraVision"
        mc:Ignorable="d"
        WindowStartupLocation="CenterScreen" WindowState="Maximized"
        Title="工具设置" Height="1020" Width="1200"
           SizeChanged="WindowX_SizeChanged"
        >
    <p:WindowX.DataContext>
        <Binding Source="{StaticResource Locator}" Path="Main.DataModel .FaraVisionDataModel.Processmodel.tool"/>
    </p:WindowX.DataContext>
    <Grid Margin="5">
        <Grid.Resources>
            <TransformGroup x:Key="Imageview">
                <ScaleTransform/>
                <TranslateTransform/>
            </TransformGroup>

            <ObjectDataProvider x:Key="testmodes" MethodName="GetValues" ObjectType="{x:Type sys:Enum}">
                <ObjectDataProvider.MethodParameters>
                    <x:Type Type="faravision:TestModes"/>
                </ObjectDataProvider.MethodParameters>
            </ObjectDataProvider>
        </Grid.Resources>

        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="auto"/>
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition/>
            <RowDefinition Height="auto"/>
        </Grid.RowDefinitions>
        <ScrollViewer  Grid.Row="0" Grid.Column="0"  HorizontalAlignment="Center" VerticalAlignment="Center" x:Name="BackFrame" 
                       Background="Transparent"  HorizontalScrollBarVisibility="Disabled" VerticalScrollBarVisibility="Disabled"  
                       Cursor="SizeAll" Margin="5" Focusable="False" >
            <ContentControl  Width="{Binding Source={StaticResource Locator},Path=Main.DataModel.FaraVisionDataModel.Processmodel.ShowBitmapSource.Width}"
                             Height="{Binding Source={StaticResource Locator},Path=Main.DataModel.FaraVisionDataModel.Processmodel.ShowBitmapSource.Height}"
                             Background="Transparent" 
                             MouseLeftButtonDown="ContentControl_MouseLeftButtonDown"  
                             MouseLeftButtonUp="ContentControl_MouseLeftButtonUp"
                             MouseMove="ContentControl_MouseMove"
                             MouseWheel="ContentControl_MouseWheel" >
                <Canvas Grid.Column="0" x:Name="show_image_canvas"  RenderTransform="{StaticResource Imageview}"   
                        RenderOptions.BitmapScalingMode="NearestNeighbor">

                    <!--<Image x:Name="show_image_ctr"   Source="{Binding Source={StaticResource Locator},Path=Main.DATA.srcbitmapsource}"  ></Image>-->
                    <Image x:FieldModifier="public" x:Name="show_image_ctr"  
                           Source="{Binding Source={StaticResource Locator},Path=Main.DataModel.FaraVisionDataModel.Processmodel.ShowBitmapSource}"
                           MouseMove="Show_image_ctr_MouseMove" Panel.ZIndex="0" 
                           Stretch="Uniform"  
                           RenderOptions.BitmapScalingMode="NearestNeighbor"  >
                        <Image.Effect>
                            <!--自带效果-->
                            <DropShadowEffect BlurRadius="100" Opacity="0.75"/>
                        </Image.Effect>
                    </Image>
                    <Rectangle x:Name="RectangleBarcode"   Visibility="{Binding TestMode,Converter={StaticResource TestMode2Visibility_Barcode}}" StrokeThickness="2"    Stroke="OrangeRed" Canvas.Left="10" Canvas.Right="10" Width="50" Height="50"    HorizontalAlignment="Left" VerticalAlignment="Top"></Rectangle>
                    <Rectangle x:Name="RectanglePosition" Visibility="{Binding TestMode,Converter={StaticResource TestMode2Visibility_ShapeMatch}}"  StrokeThickness="2"   Stroke="Orange" Canvas.Left="10" Canvas.Right="10" Width="50" Height="50"    HorizontalAlignment="Left" VerticalAlignment="Top"></Rectangle>
                    <Rectangle x:Name="RectangleDimension" Visibility="{Binding TestMode,Converter={StaticResource TestMode2Visibility_Dimension}}"   StrokeThickness="2"   Stroke="Orange" Canvas.Left="10" Canvas.Right="10" Width="50" Height="50"    HorizontalAlignment="Left" VerticalAlignment="Top"></Rectangle>

                </Canvas>

            </ContentControl>

        </ScrollViewer>

        <GroupBox Header="工具参数" Grid.Column="1">
            <Grid Grid.Column="1">
                <Grid.RowDefinitions>
                    <RowDefinition Height=" auto"/>
                    <RowDefinition Height=" auto"/>
                    <RowDefinition Height=" auto"/>
                    <RowDefinition Height=" auto"/>
                    <RowDefinition Height=" auto"/>
                    <RowDefinition/>
                </Grid.RowDefinitions>

                <Button Padding="15" Margin="5" Click="Button_Click">选择图片</Button>
                <Grid Grid.Row="1">
                    <Grid.RowDefinitions>
                        <RowDefinition/>
                        <RowDefinition/>
                        <RowDefinition/>
                        <RowDefinition/>
                        <RowDefinition/>
                        <RowDefinition/>
                        <RowDefinition/>
                        <RowDefinition/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="auto"/>
                        <ColumnDefinition MinWidth="150"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Row="0" Grid.Column="0" VerticalAlignment="Center" HorizontalAlignment="Right">工具序号:</TextBlock>
                    <TextBlock Grid.Row="1" Grid.Column="0" VerticalAlignment="Center" HorizontalAlignment="Right">工具类型:</TextBlock>
                    <TextBlock Grid.Row="2" Grid.Column="0" VerticalAlignment="Center" HorizontalAlignment="Right">工具名称:</TextBlock>
                    <TextBlock Grid.Row="3" Grid.Column="0" VerticalAlignment="Center" HorizontalAlignment="Right">触发指令:</TextBlock>
                    <TextBlock Grid.Row="4" Grid.Column="0" VerticalAlignment="Center" HorizontalAlignment="Right">相机曝光时间:</TextBlock>
                    <TextBlock Grid.Row="5" Grid.Column="0" VerticalAlignment="Center" HorizontalAlignment="Right">产品位置序号:</TextBlock>

                    <TextBlock Grid.Row="0" Grid.Column="1" Margin="2" Text="{Binding Index  }"/>
                    <ComboBox Grid.Row="1"  Grid.Column="1" Margin="2"  Text="{Binding TestMode,UpdateSourceTrigger=PropertyChanged}" IsEditable="True"  IsReadOnly="True" 
                            ItemsSource="{Binding Source={StaticResource testmodes}}"
                              />
                    <TextBox Grid.Row="2" Grid.Column="1"   Margin="2" Text="{Binding Name}" />
                    <TextBox Grid.Row="3" Grid.Column="1"   Margin="2" Text="{Binding Command}" />
                    <TextBox Grid.Row="4" Grid.Column="1"   Margin="2" Text="{Binding ExposureTime}" />
                    <TextBox Grid.Row="5" Grid.Column="1"   Margin="2" Text="{Binding ProductPositionNO}" />

                    <TextBlock Grid.Row="6" Grid.Column="0" VerticalAlignment="Center" HorizontalAlignment="Right">相机:</TextBlock>
                    <!--<ComboBox Grid.Row="6"  Grid.Column="1" Margin="2"  Text="{Binding CameraID,UpdateSourceTrigger=PropertyChanged}" IsEditable="True"  IsReadOnly="True" 
                            ItemsSource="{Binding Source={StaticResource Locator},Path=Main.DataModel.FaraVisionDataModel.Processmodel.CameraIDList}"/>-->

                    <ComboBox Grid.Row="6"  Grid.Column="1" Margin="2"  SelectedIndex="{Binding CameraIndex,UpdateSourceTrigger=PropertyChanged}" IsEditable="True"  IsReadOnly="True" 
                            ItemsSource="{Binding Source={StaticResource Locator},Path=Main.DataModel.FaraVisionDataModel.Processmodel.CameraIDList}"/>


                </Grid>
                <GroupBox Header="二维码识别" Grid.Row="2" p:GroupBoxHelper.HeaderBackground="YellowGreen" BorderBrush="YellowGreen" Margin="0,5"
                           p:GroupBoxHelper.HeaderForeground="White"
                         Visibility="{Binding TestMode,Converter={StaticResource TestMode2Visibility_Barcode}}"
                          >
                    <Grid Grid.Row="0">
                        <Grid.RowDefinitions>
                            <RowDefinition/>
                            <RowDefinition/>
                            <RowDefinition/>
                            <RowDefinition/>
                            <RowDefinition/>
                            <RowDefinition/>
                            <RowDefinition/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="auto"/>
                            <ColumnDefinition MinWidth="150"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Row="1" Grid.Column="0" VerticalAlignment="Center">二维码ROI:</TextBlock>

                        <Button Grid.Row="1" Grid.Column="1"   Margin="2" Name="SelectBarcodeROI" Click="SelectBarcodeROI_Click">选择</Button>

                        <Grid Grid.Row="2" Grid.Column="0" Margin="10,2,2,2" Grid.ColumnSpan="2">
                            <Grid.RowDefinitions>
                                <RowDefinition/>
                                <RowDefinition/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="auto"/>
                                <ColumnDefinition />
                                <ColumnDefinition Width="auto"/>
                                <ColumnDefinition />
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Row="0" Grid.Column="0" VerticalAlignment="Center">ROW1:</TextBlock>
                            <TextBlock Grid.Row="1" Grid.Column="0" VerticalAlignment="Center">ROW2:</TextBlock>
                            <TextBlock Grid.Row="0" Grid.Column="2" VerticalAlignment="Center">COL1:</TextBlock>
                            <TextBlock Grid.Row="1" Grid.Column="2" VerticalAlignment="Center">COL2:</TextBlock>

                            <TextBox Grid.Row="0" Grid.Column="1"   Margin="2" Text="{Binding BarCodeROI.Row1}" />
                            <TextBox Grid.Row="1" Grid.Column="1"   Margin="2" Text="{Binding BarCodeROI.Row2}" />
                            <TextBox Grid.Row="0" Grid.Column="3"   Margin="2" Text="{Binding BarCodeROI.Col1}" />
                            <TextBox Grid.Row="1" Grid.Column="3"   Margin="2" Text="{Binding BarCodeROI.Col2}" />

                        </Grid>

                        <TextBlock Grid.Row="3" VerticalAlignment="Center" HorizontalAlignment="Right">延时:</TextBlock>
                        <TextBlock Grid.Row="4" VerticalAlignment="Center" HorizontalAlignment="Right">结束码:</TextBlock>





                        <TextBox Grid.Row="3" Grid.Column="1" Margin="3" Text="{Binding Delaytimes,UpdateSourceTrigger=PropertyChanged}"></TextBox>
                        <TextBox Grid.Row="4" Grid.Column="1" Margin="3" Text="{Binding FinishedCode,UpdateSourceTrigger=PropertyChanged}"></TextBox>


                        <CheckBox Grid.Row="5" Grid.ColumnSpan="2" VerticalAlignment="Center" HorizontalAlignment="Center" IsChecked="{Binding MPMode}">母排扫码模式</CheckBox>
                        <CheckBox Grid.Row="6" Grid.ColumnSpan="2" VerticalAlignment="Center" HorizontalAlignment="Center" IsChecked="{Binding SendBarcode}">发送扫码内容</CheckBox>



                    </Grid>
                </GroupBox>

                <GroupBox Header="位置检测" Grid.Row="3" p:GroupBoxHelper.HeaderBackground="BlueViolet" BorderBrush="BlueViolet" Margin="0,5" 
                          VerticalAlignment="Top" IsEnabled="{Binding PositionDetect}" 
                          p:GroupBoxHelper.HeaderForeground="White"
                          Visibility="{Binding TestMode,Converter={StaticResource TestMode2Visibility_ShapeMatch}}"
                          >
                    <Grid Grid.Row="0">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="auto"/>
                            <RowDefinition Height="auto"/>
                            <RowDefinition Height="auto"/>
                            <RowDefinition Height="auto"/>
                            <RowDefinition Height="auto"/>
                            <RowDefinition Height="auto"/>
                            <RowDefinition Height="auto"/>
                            <RowDefinition Height="auto"/>
                            <RowDefinition Height="auto"/>
                            <RowDefinition Height="auto"/>
                            <RowDefinition Height="auto"/>
                            <RowDefinition Height="auto"/>
                            <RowDefinition Height="auto"/>
                            <RowDefinition Height="auto"/>
                            <RowDefinition Height="auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="auto"/>
                            <ColumnDefinition MinWidth="150"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Row="0" Grid.Column="0" VerticalAlignment="Center" HorizontalAlignment="Right" >X偏差范围:</TextBlock>
                        <TextBlock Grid.Row="1" Grid.Column="0" VerticalAlignment="Center" HorizontalAlignment="Right" >Y偏差范围:</TextBlock>
                        <TextBlock Grid.Row="2" Grid.Column="0" VerticalAlignment="Center" HorizontalAlignment="Right" >角度偏差范围:</TextBlock>
                        <TextBlock Grid.Row="3" Grid.Column="0" VerticalAlignment="Center" HorizontalAlignment="Right">模板匹配分值下限:</TextBlock>
                        <TextBlock Grid.Row="4" Grid.Column="0" VerticalAlignment="Center" HorizontalAlignment="Right"  Visibility="Collapsed">比例um/pixel:</TextBlock>
                        <TextBlock Grid.Row="5" Grid.Column="0" VerticalAlignment="Center" HorizontalAlignment="Right"  Visibility="Collapsed">X轴取反:</TextBlock>
                        <TextBlock Grid.Row="6" Grid.Column="0" VerticalAlignment="Center" HorizontalAlignment="Right"  Visibility="Collapsed">Y轴取反:</TextBlock>
                        <TextBlock Grid.Row="7" Grid.Column="0" VerticalAlignment="Center" HorizontalAlignment="Right"  Visibility="Collapsed">XY轴对换:</TextBlock>
                        <!--<TextBlock Grid.Row="6" Grid.Column="0" VerticalAlignment="Center" HorizontalAlignment="Right">取结果:</TextBlock>-->


                        <TextBlock Grid.Row="9" Grid.Column="0" VerticalAlignment="Center"  Margin="10,20,0,0" >标定位置:</TextBlock>
                        <Button  Grid.Row="9" Grid.Column="1" VerticalAlignment="Bottom" Margin="10,0,2,0" HorizontalAlignment="Right" 
                                 p:ButtonHelper.CornerRadius="10" Height="20" Width="80"
                                 Background="PaleVioletRed" Foreground="White"
                                 Name="getinitposition" Visibility="Collapsed"
                                 >标定位置</Button>

                        <Grid Grid.Row="10" Grid.Column="0"  Grid.ColumnSpan="2" Margin="10,0,0,30">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="auto"/>
                                <ColumnDefinition />
                                <ColumnDefinition Width="auto"/>
                                <ColumnDefinition />
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Row="0" Grid.Column="0" VerticalAlignment="Center">X:</TextBlock>
                            <TextBlock Grid.Row="0" Grid.Column="2" VerticalAlignment="Center">Y:</TextBlock>

                            <TextBox Grid.Row="0" Grid.Column="1"   Margin="2" Text="{Binding InitX}" />
                            <TextBox Grid.Row="0" Grid.Column="3"   Margin="2" Text="{Binding InitY}" />

                        </Grid>
                        <TextBlock Grid.Row="11" Grid.Column="0" VerticalAlignment="Center" >模板匹配ROI:</TextBlock>

                        <TextBlock Grid.Row="0" Grid.Column="1" VerticalAlignment="Center" Margin="2" Text="{Binding ModelFileName}" Visibility="Collapsed" />
                        <TextBox Grid.Row="0" Grid.Column="1"   Margin="2,2,100,2" Text="{Binding Allow_X_Delta}"  />
                        <TextBox Grid.Row="1" Grid.Column="1"   Margin="2,2,100,2" Text="{Binding Allow_Y_Delta}"  />
                        <TextBox Grid.Row="2" Grid.Column="1"   Margin="2,2,100,2" Text="{Binding AllowAngleDelta}"  />
                        <TextBlock Grid.Row="0" Grid.Column="1"  HorizontalAlignment="Right"  Margin="2,2,70,2"  Visibility="Collapsed">mm</TextBlock>
                        <TextBlock Grid.Row="1" Grid.Column="1"  HorizontalAlignment="Right"  Margin="2,2,70,2"  Visibility="Collapsed">mm</TextBlock>

                        <Button Grid.Row="2" Grid.Column="1" Width="90" Margin="2"
                                Grid.RowSpan="2" HorizontalAlignment="Right"
                                p:ButtonHelper.CornerRadius="20"
                                Background="PaleVioletRed" Foreground="White"
                                Name="ApplyAll"
                                Click="ApplyAll_Click"
                                >全部应用</Button>
                        <TextBox Grid.Row="3" Grid.Column="1" VerticalAlignment="Center" Margin="2,2,100,2" Text="{Binding MinScore}"/>
                        <TextBox Grid.Row="4" Grid.Column="1" VerticalAlignment="Center" Margin="2,2,100,2" Text="{Binding K}"  Visibility="Collapsed"/>
                        <CheckBox Grid.Row="5" Grid.Column="1" Margin="2"  IsChecked="{Binding DirectX }" Visibility="Collapsed"/>
                        <CheckBox Grid.Row="6" Grid.Column="1" Margin="2"  IsChecked="{Binding DirectY  }" Visibility="Collapsed"/>
                        <CheckBox Grid.Row="7" Grid.Column="1" Margin="2"  IsChecked="{Binding InvertXY }" Visibility="Collapsed"/>
                        <!--<CheckBox Grid.Row="6" Grid.Column="1" Margin="2"  IsChecked="{Binding SendStatus  }"/>-->



                        <Button Grid.Row="11" Grid.Column="1"    Margin="2" Name="SelectPositionROI" Click="SelectPositionROI_Click" Padding="5">选择</Button>
                        <Grid Grid.Row="12" Grid.Column="0" Margin="10,2,2,2" Grid.ColumnSpan="2">
                            <Grid.RowDefinitions>
                                <RowDefinition/>
                                <RowDefinition/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="auto"/>
                                <ColumnDefinition />
                                <ColumnDefinition Width="auto"/>
                                <ColumnDefinition />
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Row="0" Grid.Column="0" VerticalAlignment="Center" >ROW1:</TextBlock>
                            <TextBlock Grid.Row="1" Grid.Column="0" VerticalAlignment="Center">ROW2:</TextBlock>
                            <TextBlock Grid.Row="0" Grid.Column="2" VerticalAlignment="Center">COL1:</TextBlock>
                            <TextBlock Grid.Row="1" Grid.Column="2" VerticalAlignment="Center">COL2:</TextBlock>

                            <TextBox Grid.Row="0" Grid.Column="1"   Margin="2" Text="{Binding PositionROI.Row1}" />
                            <TextBox Grid.Row="1" Grid.Column="1"   Margin="2" Text="{Binding PositionROI.Row2}" />
                            <TextBox Grid.Row="0" Grid.Column="3"   Margin="2" Text="{Binding PositionROI.Col1}" />
                            <TextBox Grid.Row="1" Grid.Column="3"   Margin="2" Text="{Binding PositionROI.Col2}" />

                        </Grid>
                    </Grid>
                </GroupBox>

                <GroupBox Header="面积检测" Grid.Row="4" p:GroupBoxHelper.HeaderBackground="CadetBlue" BorderBrush="BlueViolet" Margin="0,5" 
                          VerticalAlignment="Top"  p:GroupBoxHelper.HeaderForeground="White"
                    Visibility="{Binding TestMode,Converter={StaticResource TestMode2Visibility_Dimension}}"

                    >

                    <Grid Margin="2">
                        <Grid.RowDefinitions>
                            <RowDefinition/>
                            <RowDefinition/>
                            <RowDefinition/>
                            <RowDefinition/>
                            <RowDefinition/>
                            <RowDefinition/>
                            <RowDefinition/>
                            <RowDefinition/>
                            <RowDefinition/>
                            <RowDefinition/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="auto" />
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition  Width="auto" />
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>



                        <TextBlock Grid.Row="0" Grid.Column="0" VerticalAlignment="Center" HorizontalAlignment="Right" >面积范围:</TextBlock>
                        <TextBox Grid.Row="0" Grid.Column="1"   Margin="2" Text="{Binding MinDimension}"  />
                        <TextBlock Grid.Row="0" Grid.Column="2" VerticalAlignment="Center" HorizontalAlignment="Right" >-</TextBlock>
                        <TextBox Grid.Row="0" Grid.Column="3"   Margin="2" Text="{Binding MaxDimension}"  />


                        <TextBlock Grid.Row="1" Grid.Column="0" VerticalAlignment="Center" HorizontalAlignment="Right" >面积过滤:</TextBlock>
                        <TextBox Grid.Row="1" Grid.Column="1"   Margin="2" Text="{Binding MinAreaFilter}"  />
                        <TextBlock Grid.Row="1" Grid.Column="2" VerticalAlignment="Center" HorizontalAlignment="Right" >-</TextBlock>
                        <TextBox Grid.Row="1" Grid.Column="3"   Margin="2" Text="{Binding MaxAreaFilter}"  />

                        <CheckBox  Grid.Row="2" Grid.Column="0" VerticalAlignment="Center"  Margin="2" Grid.ColumnSpan="2"
                                    IsChecked="{Binding GrayMode}" 
                                   >灰色模式</CheckBox>



                        <GroupBox Header="灰度参数" Grid.Row="4" Grid.ColumnSpan="5"   Visibility="{Binding GrayMode ,Converter={StaticResource Bool2Visible_Converter}}" >
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition/>
                                    <RowDefinition/>
                                    <RowDefinition/>
                                    <RowDefinition/>
                                    <RowDefinition/>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="auto" />
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition  Width="auto" />
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <TextBlock  Grid.Row="0" Grid.Column="0" VerticalAlignment="Center" HorizontalAlignment="Right">灰度范围:</TextBlock>
                                <TextBox Grid.Row="0" Grid.Column="1"   Margin="2" Text="{Binding MinGray}"  />
                                <TextBlock Grid.Row="0" Grid.Column="2" VerticalAlignment="Center" HorizontalAlignment="Right" >-</TextBlock>
                                <TextBox Grid.Row="0" Grid.Column="3"   Margin="2" Text="{Binding MaxGray}"  />



                            </Grid>
                        </GroupBox>

                        <GroupBox Header="彩色参数" Grid.Row="3" Grid.ColumnSpan="5" Visibility="{Binding GrayMode,Converter={StaticResource Bool2Visible_Converter_INV}}" >
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition/>
                                    <RowDefinition/>
                                    <RowDefinition/>
                                    <RowDefinition/>
                                    <RowDefinition/>
                                    <RowDefinition/>
                                    <RowDefinition/>
                                    <RowDefinition/>
                                    <RowDefinition/>
                                    <RowDefinition/>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="auto" />
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition  Width="auto" />
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <TextBlock   Grid.Row="0" Grid.Column="0" VerticalAlignment="Center" HorizontalAlignment="Right">红色通道</TextBlock>
                                <TextBlock   Grid.Row="1" Grid.Column="0" VerticalAlignment="Center" HorizontalAlignment="Right">绿色通道</TextBlock>
                                <TextBlock   Grid.Row="2" Grid.Column="0" VerticalAlignment="Center" HorizontalAlignment="Right">蓝色通道</TextBlock>

                                <TextBox Grid.Row="0" Grid.Column="1"   Margin="2" Text="{Binding MinRed}"  />
                                <TextBlock Grid.Row="0" Grid.Column="2" VerticalAlignment="Center" HorizontalAlignment="Right" >-</TextBlock>
                                <TextBox Grid.Row="0" Grid.Column="3"   Margin="2" Text="{Binding MaxRed}"  />

                                <TextBox Grid.Row="1" Grid.Column="1"   Margin="2" Text="{Binding MinGreen}"  />
                                <TextBlock Grid.Row="1" Grid.Column="2" VerticalAlignment="Center" HorizontalAlignment="Right" >-</TextBlock>
                                <TextBox Grid.Row="1" Grid.Column="3"   Margin="2" Text="{Binding MaxGreen}"  />

                                <TextBox Grid.Row="2" Grid.Column="1"   Margin="2" Text="{Binding MinBlue}"  />
                                <TextBlock Grid.Row="2" Grid.Column="2" VerticalAlignment="Center" HorizontalAlignment="Right" >-</TextBlock>
                                <TextBox Grid.Row="2" Grid.Column="3"   Margin="2" Text="{Binding MaxBlue}"  />

                            </Grid>
                        </GroupBox>


                        <TextBlock Grid.Row="5" Grid.Column="0" VerticalAlignment="Center" >面积计算ROI:</TextBlock>
                        <Button Grid.Row="5" Grid.Column="1"    Margin="2" Name="SelectDimensionROI" Click="SelectDimensionROI_Click" Grid.ColumnSpan="5" Padding="5">选择</Button>
                        <Grid Grid.Row="6" Grid.Column="0" Margin="10,2,2,2" Grid.ColumnSpan="6">
                            <Grid.RowDefinitions>
                                <RowDefinition/>
                                <RowDefinition/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="auto"/>
                                <ColumnDefinition />
                                <ColumnDefinition Width="auto"/>
                                <ColumnDefinition />
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Row="0" Grid.Column="0" VerticalAlignment="Center" >ROW1:</TextBlock>
                            <TextBlock Grid.Row="1" Grid.Column="0" VerticalAlignment="Center">ROW2:</TextBlock>
                            <TextBlock Grid.Row="0" Grid.Column="2" VerticalAlignment="Center">COL1:</TextBlock>
                            <TextBlock Grid.Row="1" Grid.Column="2" VerticalAlignment="Center">COL2:</TextBlock>

                            <TextBox Grid.Row="0" Grid.Column="1"   Margin="2" Text="{Binding DimensionROI.Row1}" />
                            <TextBox Grid.Row="1" Grid.Column="1"   Margin="2" Text="{Binding DimensionROI.Row2}" />
                            <TextBox Grid.Row="0" Grid.Column="3"   Margin="2" Text="{Binding DimensionROI.Col1}" />
                            <TextBox Grid.Row="1" Grid.Column="3"   Margin="2" Text="{Binding DimensionROI.Col2}" />

                        </Grid>
                        <Button x:Name="DimensionApply" Grid.Row="12" Grid.Column="0" Grid.ColumnSpan="1" Margin="5" Padding="5" 
                                
                                Click="DimensionApply_Click"
                                
                                >检测试模板</Button>
                        <Button x:Name="DimensionApply_Pic" Grid.Row="12" Grid.Column="1" Grid.ColumnSpan="5" Margin="5" Padding="5" 
                                Background="Orange" Foreground="White"
                                Click="DimensionApply_Pic_Click"
                                
                                >检测照片</Button>

                    </Grid>

                </GroupBox>
                <GroupBox Header="结果输出" Grid.Row="5" p:GroupBoxHelper.HeaderBackground="BlueViolet" BorderBrush="BlueViolet" Margin="0,5" 
                          VerticalAlignment="Top" p:GroupBoxHelper.HeaderForeground="White" >
                    <Grid Margin="2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="auto"/>
                            <ColumnDefinition/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition/>
                            <RowDefinition/>
                            <RowDefinition/>
                            <RowDefinition/>
                        </Grid.RowDefinitions>
                        <CheckBox Grid.Row="0" Grid.Column="0" Margin="2"  IsChecked="{Binding SendStatus  }" Content="输出结果" />
                        <!--<CheckBox Grid.Row="6" Grid.Column="1" Margin="2"  IsChecked="{Binding InvertResult  }" Content="结果取反" />-->
                        <TextBlock Grid.Row="1" Grid.Column="0" Margin="5">OK输出命令:</TextBlock>
                        <TextBlock Grid.Row="2" Grid.Column="0" Margin="5">NG1输出命令:</TextBlock>
                        <TextBlock Grid.Row="3" Grid.Column="0" Margin="5">NG2输出命令;</TextBlock>

                        <TextBox Grid.Row="1" Grid.Column="1" Margin="2" Padding="2" Text="{Binding OKCMD}"/>
                        <TextBox Grid.Row="2" Grid.Column="1" Margin="2" Padding="2" Text="{Binding NG1CMD}"/>
                        <TextBox Grid.Row="3" Grid.Column="1" Margin="2" Padding="2" Text="{Binding NG2CMD}"/>
                    </Grid>

                </GroupBox>


                <!--<CheckBox BorderBrush="White" Grid.Row="2" VerticalAlignment="Top" Margin="10,12,5,0" HorizontalAlignment="Right"
                          Foreground="White" IsChecked="{Binding SendBarcodeData}"
                          >输出条码

                </CheckBox>
                <CheckBox BorderBrush="White" Grid.Row="3" VerticalAlignment="Top" Margin="10,12,5,0" HorizontalAlignment="Right"
                          Foreground="White" IsChecked="{Binding SendPositionData}"
                          >输出位置                </CheckBox>-->
                <Button  Grid.Row="3" VerticalAlignment="Top" Margin="10,12,10,0" HorizontalAlignment="Right" 
                                 p:ButtonHelper.CornerRadius="10" Height="20" Width="80"
                                 Background="Violet" Foreground="White"
                         Name="modelsetting" Visibility="{Binding TestMode,Converter={StaticResource TestMode2Visibility_ShapeMatch}}"
                         Click="modelsetting_Click"
                                 >模型设置</Button>


            </Grid>
        </GroupBox>


        <WrapPanel Grid.Row="1" HorizontalAlignment="Center" Grid.ColumnSpan="3" >
            <TextBlock Text="{Binding Tposition.X,StringFormat=光标位置:  {0}}"/>
            <TextBlock Text="{Binding Tposition.Y,StringFormat=\,{0}}" />

            <TextBlock Margin="15,0,0,0" Text="{Binding TColor.R,StringFormat=颜色:  {0}}" ToolTip="红"/>
            <TextBlock Text="{Binding TColor.G,StringFormat=\,{0}}" ToolTip="绿"/>
            <TextBlock Margin="0,0,15,0" Text="{Binding TColor.B,StringFormat=\,{0}}" ToolTip="蓝" />

        </WrapPanel>

    </Grid>
</p:WindowX>
