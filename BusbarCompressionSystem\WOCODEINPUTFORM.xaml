﻿<p:WindowX x:Class="BusbarCompressionSystem.WOCODEINPUTFORM"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:BusbarCompressionSystem"
         xmlns:p="https://opensource.panuon.com/wpf-ui"
        mc:Ignorable="d"
        p:WindowXCaption.Background="Orange"
        p:WindowXCaption.Foreground="White"
        p:WindowXCaption.Height="60"
        Title="输入批号" Height="250" Width="350"
           WindowStartupLocation="CenterScreen"
           DataContext="{Binding Source={StaticResource Locator},Path=Main.DataModel}"
           >
    <Grid Margin="5">
        <Grid.RowDefinitions>
            <RowDefinition/>
            <RowDefinition/>
            <RowDefinition/>
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="auto"/>
            <ColumnDefinition/>
        </Grid.ColumnDefinitions>

        <TextBlock Grid.Row="0" Grid.Column="0" VerticalAlignment="Center">批号:</TextBlock>
        <TextBlock Grid.Row="1" Grid.Column="0" VerticalAlignment="Center">规格:</TextBlock>

        <TextBox p:TextBoxHelper.CornerRadius="15 " Height="30" Padding=" 15,0" Margin="5 "  Grid.Row="0" Grid.Column="1"  Text="{Binding Processmodel.wocodeinputstr,UpdateSourceTrigger=PropertyChanged}" />
        <TextBlock  Margin="5 "  Grid.Row="1" Grid.Column="1"  Text="{Binding Processmodel.PartNOID}"/>

        <Button Margin="5 "  Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="2" p:ButtonHelper.CornerRadius="15" Background="OrangeRed" Foreground="White" Click="Button_Click">下发参数</Button>
        
        
        
    </Grid>
</p:WindowX>
